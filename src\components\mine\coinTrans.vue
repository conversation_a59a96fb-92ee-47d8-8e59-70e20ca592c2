<template>
  <main>
    <header>
      <van-nav-bar :title="$t('main.coinTrans.label1')" left-arrow @click-left="$router.back()" />
    </header>

    <nav>
      <div class="block" :class="{ active: pageType === 0 }" @click="pageType = 0">{{ $t('main.coinTrans.label2') }}</div>
      <div class="block" :class="{ active: pageType === 1 }" @click="pageType = 1">{{ $t('main.coinTrans.label3') }}</div>
    </nav>

    <div class="row">
      <div>MYR{{ $t('main.lottery.label1') }}：{{ $store.state.userInfo.balance }}</div>
      <div>USDT{{ $t('main.lottery.label1') }}：{{ $store.state.userInfo.usd_balance }}</div>
    </div>


    <div class="list">
      <div class="input_wrap">
        <van-field ref="input" v-model="params.amount" type="number" :placeholder="$t('main.coinTrans.label4')" autocomplete="off" />
      </div>
      <div class="input_wrap">
        <van-field ref="input" v-model="params.password" type="password" :placeholder="$t('main.coinTrans.label5')" autocomplete="off" />
      </div>

      <div class="button" @click="submit">{{ $t('main.dealRecord.label6') }}</div>
    </div>

  </main>
</template>
<script>
export default {
  name: 'message',
  data() {
    return {
      pageType: 0,
      params: {
        from: '',
        to: '',
        amount: '',
        password: '',
      },
    }
  },
  created() {
  },
  methods: {
    async submit() {
      if(this.params.amount === '') {
        this.$parent.showTost(0, this.$t('main.coinTrans.label4'))
        return
      }
      if(this.params.password === '') {
        this.$parent.showTost(0, this.$t('main.coinTrans.label5'))
        return
      }
      this.params.from = this.pageType === 0 ? 'kwd' : 'usdt'
      this.params.to = this.pageType === 0 ? 'usdt' : 'kwd'
      const res = await this.$apiFun.coinTransApi(this.params)
      console.log(res)
      if(res.code === 200) {
        this.$parent.showTost(1, res.message)
        this.params.amount = ''
        this.params.password = ''
      }
    },
  },
};
</script>
<style lang="scss" scoped>
header {
  height: 60px;
  .van-nav-bar {
    position: fixed;
    width: 100%;
    top: 0;
  }
  .right {
    position: fixed;
    display: flex;
    align-items: center;
    z-index: 1000;
    height: 46px;
    line-height: 46px;
    right: 20px;
    font-weight: 500;
    font-size: .35rem;
  }
}

.list {
  margin: 0 15px;
  .input_wrap {
    border: 1px solid #999;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 20px;
  }
  .button {
    height: 36px;
    line-height: 36px;
    text-align: center;
    background: #EA4236;
    border-radius: 3px;
    color: #fff;
    font-size: 14px;
  }
}

nav {
  width: calc(100% - 30px);
  height: 30px;
  border: 1px solid #cf866b;
  box-sizing: border-box;
  margin: 0 15px 10px;
  display: flex;
  .block {
    flex: 1;
    text-align: center;
    line-height: 28px;
    color: #cf866b;
  }
  .active {
    background: #cf866b;
    color: #fff;
  }
}

.row {
  div{
    font-size: 16px;
    margin: 10px 15px;
  }
  
}
</style>
