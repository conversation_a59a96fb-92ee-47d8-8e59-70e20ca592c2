//引入刚才的http.js文件
import https from './http.js';

	//设置个对象，就不用一个个暴露了，直接暴露对象
	let apiFun = {};
	// 天美社区源码网 timibbs.net timibbs.com timibbs.vip
	/* 获取列表 */
	//查询列表，详情就是get
	/* /api/getlist是请求接口地址，有http.js里面的Ip加上，如：http:***********:9090//api/getlist  */
	apiFun.get = function(url,params) {
		return https.get(url, params)
	}

  apiFun.post = function(url,params) {
		return https.post(url, params)
	}
    apiFun.login = function(params) {
		let baseURLs = sessionStorage.getItem('baseURL') || '';
        if(!baseURLs){
            sessionStorage.setItem("baseURL",baseURLs)
        }
		return https.post('/api/login_pc', params)
		// return https.post('/api/login', params)
	}
	apiFun.register = function(params) {
		let baseURLs = sessionStorage.getItem('baseURL') || '';
        if(!baseURLs){
            sessionStorage.setItem("baseURL",baseURLs)
        }
		return https.post('/api/register', params)
	}


	apiFun.register2 = function(params) {
		let baseURLs = sessionStorage.getItem('baseURL') || '';
        if(!baseURLs){
            sessionStorage.setItem("baseURL",baseURLs)
        }
		return https.post('/api/v2/register', params)
	}
	//暴露出这个对象




  // 彩票列表
  apiFun.getCaipiaoListApi = (params) => {
		return https.get('/api/lottery/list', params)
  }

  // 彩票玩法
  apiFun.getCaipiaoPlayApi = (params) => {
		return https.post('/api/lottery/play', params)
  }

  // 开奖时间
  apiFun.getCaipiaoOpenTimeApi = (params) => {
		return https.post('/api/lottery/times', params)
  }

  // 获取上一次开奖结果
  apiFun.getCaipiaoLastOpenApi = (params) => {
		return https.post('/api/lottery/loadopencode', params)
  }

  // 获取彩种开奖列表
  apiFun.getCaipiaoOpenCodeApi = (params) => {
		return https.post('/api/lottery/opencode', params)
  }

  // 投注
  apiFun.lotterySubmitApi = (params) => {
		return https.post('/api/lottery/submit_order', params)
  }

  // 投注列表
  apiFun.getBetListApi = (params) => {
		return https.get('/api/lottery/order', params)
  }







  // 流水類型
  apiFun.getDealRecordTypeApi = (params) => {
		return https.get('/api/money_change/type', params)
  }

  // 流水
  apiFun.getDealRecordListApi = (params) => {
		return https.get('/api/money_change', params)
  }

  // 流水2
  apiFun.getDealRecordList2Api = (params) => {
		return https.get('/api/return_agent_money_change', params)
  }




  // 劃賬
  apiFun.transApi = (params) => {
		return https.post('/api/agent_transfer', params)
  }





  // 團隊信息
  apiFun.getAgentTeamInfoApi = (params) => {
		return https.get('/api/agent/team/info', params)
  }

  // 團隊信息 - 提款
  apiFun.getAgentTeamFundsApi = (params) => {
		return https.get('/api/agent/team/funds', params)
  }

  // 设置邀请码
  apiFun.agentSaveApi = (params) => {
		return https.post('/api/agent/invite_code/save', params)
  }

  // 劃賬
  apiFun.transApi = (params) => {
		return https.post('/api/agent_transfer', params)
  }
  // 下级
  apiFun.agentChildrenApi = (params) => {
    return https.get('/api/agent/children', params)
  }



  // 货币兑换
  apiFun.coinTransApi = (params) => {
		return https.post('/api/balance_transfer', params)
  }



  // 游戏列表
  apiFun.getSonGameApi = (params) => {
		return https.post('/api/getSonGame', params)
  }



  // 取消充值
  apiFun.cancelRechargeApi = (params) => {
		return https.post('/api/recharge_cancel', params)
  }

  // 取消提款
  apiFun.cancelWithdrawApi = (params) => {
		return https.post('/api/withdraw_cancel', params)
  }


  // 盈亏
  apiFun.getYingkuiApi = (params) => {
    return https.post('/api/winloss', params)
  }

  apiFun.getYingkui2Api = (params) => {
    return https.post('/api/winloss/month', params)
  }


export default apiFun;

