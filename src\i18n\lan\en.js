
export const foot = {
  index: 'Home',
  activity: 'Offers',
  kefu: 'Customer Service',
  zanzhu: 'Sponsorship',
  mine: 'My',
  loading: 'Loading',
}


export const mode = {
  activity: {
    title: 'Promotions',
    total: 'All',
  },

  app: {
    title: 'app download',
    label1: 'You can scan the QR code to download the app',
    label2: 'Download now',
    label3: 'You need to download, install and register in the same network environment, please do not switch networks; If you cannot install normally, please use the browser on your phone to open this page',
  },

  concise: {
    title1: 'OB Electronics',
    title2: 'FG Electronics',
    title3: 'PP Electronics',
    title4: 'AE Electronics',
  },

  hongbao: {
    label1: 'Remaining number of redemptions',
    label2: 'Times',
    label3: 'Already received',
    label4: 'Current maximum number of redemptions',
    label5: 'Go and meet the conditions!',
    label6: 'Event time',
    label7: 'Cumulative recharge amount',
    label8: 'Red envelope number',
    label9: 'Rule for redemption:',
    label10: '1. After grabbing the red envelope, the system automatically pays out the prize',
    label11: 'Arrival in seconds',
    label12: 'Withdrawal is available when turnover multiples are reached;',
    label13: '2. Red envelope conditions: The recharge amount must be within the specified event date (Eastern Time). According to the cumulative recharge amount, the number of red envelope grabbing times can be obtained, and the corresponding number of red envelopes can be grabbed;',
    label14: 'Jinsha Group Official Website',
    label15: 'Note:',
    label16: 'Grab red envelopes',
    label17: 'Beijing time is from 10 am to 12 pm every day, and deposits are calculated from 12 pm the previous day to 10 am today. Thank you',
    label18: '1. Members must obtain the corresponding number of red envelope grabbing times according to the cumulative recharge amount within the specified event date (Eastern Time). If the deposit amount range is not reached within the specified time range, the number of red envelope grabbing times will not be calculated and will be invalid after the expiration!',
    label19: '2. All event offers are specially designed for players. If any group or individual is found to be dishonestly obtaining bonuses or any threats or abuse of company offers, the company reserves the right to freeze and cancel the group or individual account and account balance.',
    label20: '3. Jinsha Group Official Website reserves all rights of interpretation and can change, stop, and cancel promotional activities at any time.',
    label21: 'Millions of cash bonuses every day! The more you deposit, the more opportunities you will have. You will get endless red envelopes and surprises. What are you waiting for? Hurry up and call your friends to share with you!!',
    label22: 'Current time period',
    label23: 'The event has not started yet, please wait for it to start.',
    label24: 'Please wait for the next event.',
    label25: 'Congratulations!',
    label26: 'Get',
    label27: 'MYR',
    label28: 'Recharge Now',
    toast1: 'You have not yet met the conditions for receiving the prize, please complete it!',
    toast2: 'Server abnormality, please try again later',
  },

  index: {
    label1: 'Live entertainment, sports betting, electronic games, etc. are all at your fingertips',
    label2: 'Customer Service',
    label3: 'More Announcements',
    label4: 'Not logged in',
    label5: 'Log in to view',
    label6: 'Deposit',
    label7: 'Transfer',
    label8: 'Withdrawal',
    label9: 'Live',
    label10: 'Sports',
    label11: 'Esports',
    label12: 'Chess and Cards',
    label13: 'Electronic',
    label14: 'Lottery',
    label15: 'Hi, welcome to enter',
    label16: 'Exclusive VIP experience',
    label17: 'Enjoy member privileges immediately',
    label18: 'Enjoy the uniqueness that belongs only to you',
    label19: 'Member Center',
    label20: 'Message Center',
    label21: 'Feedback',
    label22: 'Permanent domain name',
    label23: 'Log in now',
    label24: 'Safe exit',
    label25: 'Welcome to',
    label26: 'Fishing',
    label27: 'More Notice',
    label28: '',
    toast1: 'Copy successful!',
    windowTitle: 'Reminder',
    windowLabel1: 'TNG and USDT deposit and withdrawal method are now available on the Melco International platform.',
    windowLabel2: 'Thank you for your support.'
  },

  mine: {
    title: 'My',
    kefu: 'Customer Service',
    label1: 'Center Wallet',
    label2: 'VIP Privileges',
    label3: 'Gifts',
    label4: 'My Wallet',
    label5: 'Transaction Records',
    label6: 'Betting Records',
    label7: 'Account Settings',
    label8: 'Message Center',
    label9: 'Rebate Center',
    label10: 'Activity Records',
    label11: 'Benefit Center',
    label12: 'Joint Plan',
    label13: 'Join Us, Win Wealth Together',
    label14: 'Agent Login',
    label15: 'App Download',
    label16: 'Help Center',
    label17: 'Log out of the system',
    label18: 'Join',
    label19: 'No.',
    label20: 'Day',
    label21: 'Language',
    label22: 'Usdt Wallet',
    label23: 'Commission Wallet',
  },

  transfer: {
    title: 'Transfer',
    label1: 'One-click recovery',
    label2: 'Login',
    label3: 'Register',
    label4: 'Game platform',
    label5: 'Free transfer mode',
    label6: 'Quota conversion',
    label7: 'Transfer in',
    label8: 'Transfer out',
    label9: 'Submit',
    label10: 'Credit conversion',
    label11: 'Platform account balance',
    label12: 'Game account balance',
    label13: 'Operation amount',
    label14: 'Operation amount',
    label15: 'Fill in the operation amount',
    label16: 'Transfer method',
    label17: 'Platform wallet',
    toast1: 'Please enter the operation amount!',
    toast2: 'Operation successful!',
  },

  zanzhu: {
    title: 'Sponsorships',
    label1: 'Juventus',
    label2: 'Official Regional Partner',
    label3: 'Aston Villa',
    label4: 'Official Global Premier Partner',
    label5: 'No more',
    label6: '',
    label7: '',
    label8: '',
    label9: '',
    label10: '',
    label11: '',
    label12: '',
    label13: '',

  },
}


export const main = {
  activityInfo: {
    title: 'Event details',
    label1: 'Event description',
    label2: 'Apply now',
    label3: 'Go to login',
  },

  activityRecord: {
    title: 'Activity application record',
    label1: 'No more',
    label2: 'Activity title:',
    label3: 'Application time:',
    label4: 'Status:',
    label5: 'Empty',
    label6: 'Not agreed',
    label7: 'Waiting for review',
    label8: 'Approved',
    label9: 'Rejected',
    label10: 'Not agreed',
  },

  addBankCard: {
    title: 'Add a new bank card',
    label1: 'Cardholder name',
    label2: 'In order to quickly receive your funds, please make sure that the name you fill in is consistent with the name on the bank card',
    label3: 'Bank type',
    label4: 'Bank account number',
    label5: 'Opening bank',
    label6: 'Payment password',
    label7: 'Confirm to add',
    label8: 'Bank type',
    placeholder1: "Please enter the cardholder's name",
    placeholder2: 'Please select the bank type',
    placeholder3: 'Please enter the bank number',
    placeholder4: 'Please enter the bank where the account is opened',
    placeholder5: 'Please enter the payment password',
    toast1: 'Please enter the name',
    toast2: 'Please enter the bank',
    toast3: 'Please enter the bank address where the account is opened',
    toast4: 'Please enter the bank number',
    toast5: 'Please enter the payment password',
    toast6: 'Please enter the correct card number length',
    toast7: 'Please enter the payment password length',
    toast8: 'Binding successful',
  },

  addUsdtCard: {
    title: 'Add USDT address',
    label1: 'USDT price is stable 流通性高 不受监管',
    label2: 'Learn more',
    label3: 'Bind protocol address',
    label4: 'Exchange transfer',
    label5: 'Complete withdrawal',
    label6: 'Wallet protocol',
    label7: 'USDT address',
    label8: 'Payment password',
    label9: 'Confirm to add',
    placeholder1: 'Please enter USDT address',
    placeholder2: 'Please enter payment password',
    toast1: 'Please enter USDT address',
    toast2: 'Please select wallet protocol',
    toast3: 'Please enter payment password',
  },

  applyagent: {
    title: 'Joint Venture Plan',
    label1: 'Joint Venture Department',
    label2: 'Consult Now',
    label3: 'Consult',
    label4: 'Join Us',
    label5: 'User Name',
    label6: 'Real Name',
    label7: 'Contact Information',
    label8: 'Reason for Application',
    placeholder1: 'Please enter your contact information',
    placeholder2: 'Please enter application description',
    toast1: 'Please enter a valid mobile phone number',
    toast2: 'Please enter reason for application',
  },

  betRecord: {
    title: 'Betting records',
    label1: 'Order number:',
    label2: 'Amount',
    label3: 'Payment',
    label4: 'Nothing',
    label5: 'Today',
    label6: 'Last 7 days',
    label7: 'Last 15 days',
    label8: 'Last 30 days',
    label9: 'No more',
    label10: 'Invalid bets',
    label11: 'Settled',
    label12: 'Unsettled',
    label13: 'All platforms',
  },

  boutBallBet: {
    label1: 'Dear member users',
    label2: 'Good morning, welcome to the Help Center',
    label3: 'If the relevant issues are still not resolved, please consult the online customer service',
    label4: 'About us',
    label5: 'Frequently Asked Questions',
    label6: 'Privacy Policy',
    label7: 'Disclaimer',
    label8: 'Contact us',
    label9: 'Agent Joining',
    label10: 'Gambling Responsibility',
    label11: "Didn't find a solution?",
    label12: 'Manual customer service',
    label13: 'Solution',
  },

  boutBallBetInfo: {
    label4: 'FAQ',
    label5: 'Privacy Policy',
    label6: 'Disclaimer',
    label7: 'Contact Us',
    label8: 'Agent Recruitment',
    label9: 'About Us',
    label10: 'Gambling Responsibility',
    label11: "Didn't find a solution?",
  },

  fanshui: {
    title: 'Rebate Center',
    label1: 'Rebate Record',
    label2: 'Click to receive',
    label3: 'Total received',
    label4: 'To be received',
    label5: 'Rebate amount',
    label6: 'Rebate time',
    label7: 'Receipt time:',
    label8: 'Empty',
    label9: 'No more',
    label10: 'Not received yet',
    toast1: 'No amount to be received yet!',
    label12: '',
  },

  login: {
    title: 'Login',
    label1: 'Go shopping first',
    label2: 'Online customer service',
    label3: 'Register new user',
    label4: 'Return to login',
    label5: 'Register',
    label6: 'Remember account/password',
    placeholder1: 'Phone number',
    placeholder2: 'Password',
    placeholder3: 'Verification code',
    placeholder4: 'Phone number',
    placeholder5: 'Login password',
    placeholder6: 'Confirm password',
    placeholder7: 'Real name',
    placeholder8: 'Payment password',
    placeholder9: 'Verification code',
    placeholder10: 'Invitation code',
    toast1: 'Phone number length is 6~16 characters, composed of letters or numbers!',
    toast2: 'Please enter the correct password length, at least 6 characters!',
    toast3: 'The two passwords are inconsistent!',
    toast4: 'Please enter your real name!',
    toast5: 'Please enter the correct payment password length, at least 6 characters!',
    toast6: 'Please enter the verification code!',
    toast7: 'Wrong verification code!',
    toast8: 'Please enter your account and password!',
    toast9: 'Please enter the invitation code!',
  },

  message: {
    title: 'Message Center',
    label1: 'Announcement',
    label2: 'Internal Message',
    label3: 'No More',
  },

  money: {
    title: 'My Wallet',
    label1: 'Total Assets (MYR)',
    label2: 'Central Wallet (MYR)',
    label3: 'Central Wallet',
    label4: 'Game Wallet',
    label5: 'Deposit',
    label6: 'Transfer',
    label7: 'Withdrawal',
    label8: 'Card Management',
    label9: 'Venue Balance',
    label10: 'One-Click Recycling',
    label11: 'Refresh',
  },

  password: {
    title1: 'Change login password',
    title2: 'Set withdrawal password',
    label1: 'Original password',
    label2: 'New password',
    label3: 'Confirm new password',
    label4: 'Confirm change',
    placeholder1: 'Please enter current password',
    placeholder2: 'Please enter new password',
    placeholder3: 'Please enter password again',
    toast1: 'Please enter old password',
    toast2: 'Please enter new password',
    toast3: 'Please enter the correct length of old password',
    toast4: 'Please enter the correct length of new password',
    toast5: 'Please enter confirmation password',
    toast6: 'The two passwords do not match!',
    toast7: 'The old and new passwords cannot be the same!',
    toast8: 'Password change successful!',
  },

  payInfo: {
    label1: 'Recharge information',
    label2: 'MYR',
    label3: 'Please',
    label4: 'Complete payment within',
    label5: 'After successful payment, the money will be automatically credited to your account!',
    label6: 'If you have any questions, please',
    label7: 'Contact customer service',
    label8: 'Confirm',
    label9: 'Payment address',
    label10: 'Copy',
    label11: 'Order number',
    label12: 'Transaction time',
    label13: 'Recharge method',
    label14: 'Wallet agreement',
    label15: 'Recharge completed',
    label16: 'Fund details',
    label17: 'Copy successfully!',
  },

  recharge: {
    title: 'Deposit',
    label1: 'Online Bank Transfer',
    label2: 'WeChat',
    label3: 'Alipay',
    label4: 'Receiving Account',
    label5: 'Copy',
    label6: 'Bank Account Name',
    label7: 'Opening Bank',
    label8: 'Bank Address',
    label9: 'Opening Bank',
    label10: 'Depositor Name',
    label11: 'For prompt payment, please enter the correct depositor name',
    label12: 'Bank Account Number',
    label13: 'Opening Bank Address',
    label14: 'Deposit Amount',
    label15: 'MYR',
    label16: 'USDT price is stable, high liquidity, unregulated',
    label17: 'Learn more',
    label18: 'Bind protocol address',
    label19: 'Exchange transfer',
    label20: 'Complete withdrawal',
    label21: 'Wallet agreement',
    label22: 'Deposit amount',
    label23: 'Reference exchange rate',
    label24: 'Tips',
    label25: 'Please select the correct USDT payment agreement. If you select the wrong payment agreement, the platform will not be able to receive your payment, and we will not be responsible for this!',
    label26: 'Deposit amount',
    label27: 'Deposit now',
    label28: 'Have problems with deposits? Contact',
    label29: 'Manual customer service',
    label30: 'Solution',
    label31: 'Warm reminder',
    label32: 'If you have not bound any wallet cards, please go to bind!',
    label33: 'Go to bind',
    label34: 'Please enter the amount in',
    label35: 'between!',
    label36: 'Receiving Account',
    label37: 'After completing a direct bank transfer to the account below, please fill out the information below.',
    label38: 'Deposit certificate photo',
    label39: 'Reminder: ',
    label40: 'Minimum Top-up is RM500',
    label41: 'Minimum Top-up is USDT100',
    placeholder1: 'Select the bank where you opened an account',
    placeholder2: 'Please enter the name of the depositor',
    placeholder3: 'Please enter the bank account number',
    placeholder4: 'Please enter the bank address where you opened an account',
    placeholder5: 'Please enter the withdrawal amount',
    placeholder6: 'Please enter the USDT address',
    placeholder7: 'Please enter the withdrawal amount',
    placeholder8: 'Please enter the withdrawal amount',
    placeholder9: 'Please upload a deposit certificate photo',
    toast1: 'Please enter the name of the depositor',
    toast2: 'Please enter the bank type',
    toast3: 'Please enter the bank account number',
    toast4: 'Please enter the bank address where you opened an account',
    toast5: 'Submit successfully, waiting for review',
    toast6: 'Copy successfully!',
    other1: 'Wallet address',
    other2: 'Remarks',
    other3: 'Please enter remarks',
  },

  register: {
    toast1: 'Please read and agree to the relevant terms and privacy agreement!',
    toast2: 'Username length is 6~16 characters, composed of letters or numbers!',
    toast3: 'Please enter the correct password length, at least 6 characters!',
    toast4: 'The two passwords are inconsistent!',
    toast5: 'Please enter your real name!',
    toast6: 'Please enter the correct payment password length, at least 6 characters!',
  },


  transfer: {
    title: 'Transfer',
    label1: 'Wallet amount',
    label2: 'One-click recycling',
    label3: 'Central wallet',
    label4: 'Game wallet',
    label5: 'Collapse',
    label6: 'Expand',
    label7: 'Automatic free transfer',
    label8: 'After opening, the balance is automatically transferred to the game venue',
    label9: 'Wallets within the venue do not support mutual transfer',
    label10: 'Maximum amount',
    label11: 'Transfer now',
    label12: 'Have problems with transfer? Contact',
    label13: 'Customer service',
    label14: 'Solution',
    label15: 'Choose a wallet',
    label16: 'Platform wallet',
    toast1: 'Please enter the amount of the transaction!',
    toast2: 'Wallets within the venue do not support transfers',
    toast3: 'Please enter the amount of the transaction!',
    toast4: 'The transaction was successful!',
    placeholder1: 'Please enter the amount of the transaction',
  },

  transRecord: {
    title: 'Transaction records',
    label1: 'No more',
    label2: 'Order number:',
    label3: 'Amount',
    label4: 'Empty',
    label5: 'Today',
    label6: 'Last 7 days',
    label7: 'Last 15 days',
    label8: 'All',
    label9: 'Deposit',
    label10: 'Withdrawal',
    label11: 'Transfer in',
    label12: 'Transfer out',
    label13: 'All platforms',
    label14: 'Undefined',
    label15: 'Waiting for review',
    label16: 'Review passed',
    label17: 'Review rejected',
    label18: 'Failed',
    label19: 'Successful',
    label20: 'Waiting for settlement',
    label21: 'Undefined',
    label22: 'Revoked',
    label23: 'Cancelled',
    label24: 'Recharge record',
    label25: 'Withdrawal record'
  },

  usdtmore: {
    title: 'Differences between protocols',
    label1: 'Protocol introduction',
    label2: 'TRC20: Based on the TRON network protocol',
    label3: 'TRC20 has the lowest withdrawal fee, which means that users can enjoy the exchange withdrawal service with low fees. At the same time, the TPS of the TRON network can reach thousands of levels, and transactions can be confirmed in seconds.',
    label4: 'ERC20: Based on the Ethereum network protocol',
    label5: 'Ethereum issues its own native tokens and other tokens. But the rules of thousands of tokens are different, which is very unfavorable for the development of the later market. So the token issuer made a smart contract standard, which is ERC20.',
    label6: 'Differences between protocols?',
    label7: 'Difference points',
    label8: 'TRC20 protocol',
    label9: 'ERC20 protocol',
    label10: 'Address style',
    label11: 'USDT address starts with T',
    label12: 'USDT address starts with 0x',
    label13: 'Network used',
    label14: 'TRON network',
    label15: 'Ethereum network',
    label16: 'Network status',
    label17: 'Basically not congested',
    label18: 'Often congested',
    label19: 'Transfer speed',
    label20: 'Extremely fast',
    label21: 'A few seconds to a few minutes',
    label22: 'A few minutes to tens of minutes',
    label23: 'Transaction fee',
    label24: 'Low',
    label25: 'Normal',
    label26: 'Safety factor',
    label27: 'Normal',
    label28: 'High',
    label29: 'Usage suggestions',
    label30: 'Small amount and high frequency',
    label31: 'Transaction recommendation',
    label32: 'Medium amount',
    label33: 'Regular transaction recommendation',
    label34: 'Which protocol is better?',
    label35: 'Recommended for small transactions',
    label36: 'Low fees, seconds to the account.',
    label37: 'Recommended for medium amounts',
    label38: 'Both the fees and speed are in the middle.',
    label39: 'The USDT addresses corresponding to the two protocols are not interoperable. When performing transfers, recharges, etc., you should carefully check the correct address!',
    label40: 'Normal',
  },

  vip: {
    title: 'VIP privileges',
    label1: 'Cumulative deposits',
    label2: 'Flow requirements',
    label3: 'VIP maximum rebate ratio',
    label4: 'VIP level',
    label5: 'Live-action',
    label6: 'Sports',
    label7: 'Esports',
    label8: 'Chess and cards',
    label9: 'Electronic',
    label10: 'Lottery',
    label11: 'Activity rules',
    label12: 'Promotion standards',
    label13: 'If the cumulative deposits and cumulative flow of members meet the requirements of the corresponding level, they can be promoted to the corresponding VIP level before 24:00 the next day.',
    label14: 'Promotion order',
    label15: 'VIP level can be promoted one level every day if it meets the corresponding requirements, but VIP level cannot be promoted by leaps and bounds.',
    label16: 'Retention requirements',
    label17: 'After reaching a certain VIP level, members need to complete the retention turnover requirements within 90 days. If the promotion is completed during this period, the retention requirements will be recalculated according to the current level.',
    label18: 'Demotion standards',
    label19: 'If a member does not complete the corresponding relegation turnover within a quarter (calculated as 90 days), the system will automatically downgrade one level, and the corresponding rebate and other offers will also be adjusted to the downgraded level.',
    label20: 'VIP rebate',
    label21: "The VIP rebate discount amount is calculated based on the member's valid bets between 00:00 and 23:59 Beijing time on the same day. All bet rebates on the same day will be issued to the welfare center within 24 hours after the end of the day of the bet settlement. Enter the personal center and click on the welfare center to manually collect it. (VIP rebate 1x turnover can be withdrawn)",
    label22: 'Opel Entertainment reserves the right to modify, stop and make the final interpretation of the event',
    label23: 'Already met the standard',
    label24: 'Not met the standard',
  },

  userCent: {
    title: 'Personal information',
    label1: 'Complete account information, more secure',
    label2: 'Complete personal information',
    label3: 'The more complete the information, the more thoughtful our service',
    label4: 'To improve',
    label5: 'Card management',
    label6: 'If you need to withdraw cash, please bind a bank card or virtual currency address',
    label7: 'Login password management',
    label8: 'Regularly change the login password to facilitate account security',
    label9: 'Withdrawal password management',
    label10: 'Regularly change the login password to facilitate account security',
  },

  userInfo: {
    title: 'Personal information',
    label1: 'Personal avatar',
    label2: 'Username',
    label3: 'Real name',
    label4: 'Date of birth',
    label5: 'Mobile number',
    label6: 'Email',
    label7: 'Confirm modification',
    placeholder1: 'Please enter the username',
    placeholder2: 'Please enter the real name',
    placeholder3: 'Please select the entry and exit date',
    placeholder4: 'Bind the mobile number to ensure the security of the account',
    placeholder5: 'Bind the email address to protect the security of the account',
    toast1: 'Please enter the correct mobile number',
    toast2: 'Please enter the correct email address',
    toast3: 'Please enter the correct date format: YYYY-MM-DD',
    toast4: 'Operation successful',
  },

  wallet: {
    title: 'Card Management',
    label1: 'Virtual Currency',
    label2: 'Bank Card',
    label3: 'Add USDT Address',
    label4: 'Supports adding up to 5 addresses',
    label5: 'Add Bank Card',
    label6: 'Supports adding up to 5 bank cards',
    toast1: 'Tips',
    toast2: 'Are you sure you want to unbind this card?',
    toast3: 'Unbinding Successful',
  },

  welfare: {
    title: 'Benefit Center',
    label1: 'Red Envelope Record',
    label2: 'Go to receive',
    label3: 'Number of remaining receipts:',
    label4: 'Number of receipts:',
    label5: 'Recharge Amount',
    label6: 'Red Envelope Amount:',
    label7: 'Recharge Time',
    label8: 'Receive Time:',
    label9: 'Nothing',
    label10: 'No more',
  },

  withdrawal: {
    title: 'Withdrawal',
    label1: 'USDT withdrawal',
    label2: 'Bank card withdrawal',
    label3: 'Wallet amount',
    label4: 'One-click recovery',
    label5: 'Central wallet',
    label6: 'Game wallet',
    label7: 'Collapse',
    label8: 'Expand',
    label9: 'Select USDT address',
    label10: 'Add USDT address',
    label11: 'Coded amount',
    label12: 'Withdrawal amount',
    label13: 'Maximum amount',
    label14: 'Payment password',
    label15: 'Per transaction fee',
    label16: 'Converted to USDT',
    label17: 'Reference exchange rate:',
    label18: 'Real-time changes',
    label19: 'Actual arrival:',
    label20: 'Select bank card',
    label21: 'Add bank card',
    label22: 'Please select bank card',
    label23: 'Please select USDT address',
    label24: 'Withdraw immediately',
    label25: 'Have problems with withdrawal? Contact',
    label26: 'Manual customer service',
    label27: 'Solution',
    label28: 'Commission Withdrawal',
    label29: 'Reminder: ',
    label30: 'Minimum withdrawal is RM50',
    placeholder1: 'Coded amount',
    placeholder2: 'Please enter withdrawal amount',
    placeholder3: 'Please enter payment password',
    toast1: 'Please select the bank card you want to withdraw to',
    toast2: 'Single withdrawal cannot be less than 50 RM',
    toast3: 'Please enter your payment password',
    toast4: 'Submit successfully, waiting for review',
    toast5: 'Please select USDT address',
    toast6: 'Single withdrawal cannot be less than 10 usdt',
    toast7: 'Please enter your payment password',
    toast8: 'Submit successfully, waiting for review',
    errorTitle: 'Reminder',
    errorContent: "In order to cooperate with the Malaysian government's anti-money laundering, the cryptocurrency channel (USDT) is temporarily closed and the opening time will be notified separately."
  },

  lottery: {
    label1: 'Balance',
    label2: 'Period',
    label3: 'Period Deadline',
    label4: 'Clear',
    label5: 'Bet Now',
    label6: 'Current Number',
    label7: 'Amount Per Bet',
    label8: 'Maximum Winning',
    label9: 'Total',
    label10: 'Periods',
    label11: 'Prize Number',
    label12: 'Sum Value',
    label13: 'Distance',
    label14: 'Odds',
    label15: 'Notes',
    label16: 'No More',
    toast1: 'Please Select Betting Points',
    toast2: 'Please Enter Betting Amount',
    toast3: 'Period Betting Time Expired',
    pk10Type1: '1 to 10',
    pk10Type2: 'Champion plus second place total',
  },
  
  dealRecord: {
    label1: 'Account Change Record',
    label111: 'Commission Change Record',
    label2: 'My',
    label3: 'Subordinates',
    label4: 'Amount',
    label5: 'Available Balance',
    label6: 'Confirm',
    label7: 'Cancel',
    label8: 'Please Select Start Time',
    label9: 'Please Select End Time',
    label10: 'All',
    label11: 'People',
    toast1: 'Start Time and End Time Must Be in the Same Month',
    toast2: 'Start Time Cannot Be Greater Than End Time',
  },

  coinTrans: {
    label1: 'Currency Exchange',
    label2: 'MYR to USDT',
    label3: 'USDT to MYR',
    label4: 'Please enter the exchange amount',
    label5: 'Please enter the fund password',
  },

  agent: {
    label1: 'Agent Center',
    label2: 'Overview',
    label3: 'Invitation Code',
    label4: 'Report',
    label5: 'Team Number',
    label6: 'Agent Number',
    label7: 'Player Number',
    label8: 'Team Balance',
    label9: 'Please enter the invitation code',
    label10: 'Deposit',
    label11: 'Withdrawal',
  },

  trans: {
    label1: 'Debit',
    label2: 'Please enter the subordinate account',
    label3: 'Please enter the debit amount',
    label4: 'Please enter the fund password',
  },

  betList: {
    label1: 'Betting List',
    label2: 'Number of Periods',
    label3: 'Betting Content',
    label4: 'Purchase Success',
    label5: 'Won',
    label6: 'Not won',
    label7: 'Not drawn',
    label16: 'Betting order number',
    label17: 'Game name',
    label18: 'Game play',
    label19: 'Betting time',
    label20: 'Game account',
    label21: 'Betting period number',
    label22: 'Betting number',
    label23: 'Betting amount',
    label24: 'Winning number',
    label25: 'Order status',
    label28: 'Lottery details',
    label29: 'Won',
    label30: 'Not won',
    label31: 'Not drawn',
    label32: 'Winning amount',
    label33: 'Period',
  },
}


export const other = {
  label1: 'Tips',
  label2: 'Are you sure you want to log out?',
  label3: 'Please log in!',
  label4: 'Copy successfully!',
  label5: 'Wonderful content is waiting for you to experience, come and log in!',
  label6: 'Red envelope is closed',
  label7: 'Already on the current page!',
  label8: 'Authentication failed',
  label9: 'Your account login has expired, please log in again',
}

export const yingkui = {
  label1: 'Profit and loss',
  label2: 'Today',
  label3: 'Yesterday',
  label4: 'The day before yesterday',
  label5: 'The past seven days',
  label6: 'The past 15 days',
  label7: 'Profit',
  label8: 'Bet amount',
  label9: 'Winning amount',
  label10: "Yesterday's profit",
  label11: "Please select month",
}

export const kefu = {
  tip1: 'Click on the customer center of the messenger you want below to connect immediately.',
  tip2: 'If have any questions, can have 1:1 consultation via Telegram or WhatsApp customer service. If you click the chat tool ID, you will be automatically connected to the coresponding channel and provide real-time consultation support. ',
  tip3: '※Please understand that the response may belayed offside of business hours',
  error: 'Sorry, this software does not support services in this region yet.'
}