
export const foot = {
  index: '홈',
  activity: '이벤트',
  kefu: '고객센터',
  zanzhu: '후원',
  mine: '내 정보',
  loading: '로딩 중',
}


export const mode = {
  activity: {
    title: '이벤트 혜택',
    total: '전체 보기',
  },

  app: {
    title: '앱 다운로드',
    label1: '아래 QR 코드를 스캔하면 앱을 다운로드할 수 있어요!',
    label2: '지금 다운로드',
    label3: '설치 및 가입은 동일한 네트워크에서 진행해주세요. 네트워크를 변경하면 문제가 발생할 수 있어요! 만약 정상적으로 설치되지 않으면, 휴대폰 기본 브라우저에서 이 페이지를 열어주세요.',
  },

  concise: {
    title1: 'OB슬롯',
    title2: 'FG슬롯',
    title3: 'PP슬롯',
    title4: 'AE슬롯',
  },

  hongbao: {
    label1: '남은 수령 가능 횟수',
    label2: '회',
    label3: '이미 수령하셨습니다.',
    label4: '최대 수령 가능 횟수',
    label5: '조건을 충족하신 후 참여해 주세요.',
    label6: '이벤트 기간',
    label7: '누적 충전 금액',
    label8: '받을 수 있는 보너스 횟수',
    label9: '수령 규칙:',
    label10: '1. 보너스를 받으면 시스템에서 자동 지급됩니다.',
    label11: '즉시 입금 완료',
    label12: '베팅 조건을 충족하면 출금이 가능합니다.',
    label13: '2. 보너스 수령 조건: 이벤트 기간(미국 동부 시간) 내에 누적 충전 금액에 따라 보너스 횟수를 획득할 수 있으며, 해당 횟수만큼 보너스를 받을 수 있습니다.',
    label14: '그룹 공식 사이트',
    label15: '유의사항:',
    label16: '보너스 받기',
    label17: '베이징 시간 기준 매일 오전 10시부터 12시까지 진행되며, 충전 금액은 전날 12시부터 당일 오전 10시까지 합산됩니다.',
    label18: '1. 이벤트 기간(미국 동부 시간) 내에 누적 충전 금액에 따라 보너스 횟수를 획득할 수 있으며, 지정된 기간 내 충전 금액이 조건을 충족하지 못할 경우 보너스 횟수가 제공되지 않으며, 기한 초과 시 무효 처리됩니다.',
    label19: '2. 모든 이벤트 혜택은 개별 이용자를 위한 것이며, 특정 그룹이나 개인이 부정한 방식으로 보너스를 악용하거나 회사의 혜택을 남용하는 경우, 해당 계정 및 잔액이 동결되거나 취소될 수 있습니다.',
    label20: '3. 그룹은 모든 이벤트의 최종 해석 권한을 보유하며, 언제든지 이벤트를 변경, 중단 또는 취소할 수 있습니다.',
    label21: '매일 백만원 상당의 보너스 지급! 충전하실수록 더 많은 기회가 주어집니다. 보너스와 특별한 혜택을 놓치지 마세요! 지금 바로 친구들과 함께 참여해 보세요.',
    label22: '현재 시간대',
    label23: '이벤트가 아직 시작되지 않았습니다. 잠시만 기다려 주세요.',
    label24: '다음 이벤트를 기다려 주세요.',
    label25: '축하드립니다!',
    label26: '보너스를 받으셨습니다!',
    label27: '원',
    label28: '지금 충전하기',
    toast1: '아직 수령 조건을 충족하지 못하셨습니다. 빠르게 완료해 주세요.',
    toast2: '서버 오류가 발생했습니다. 잠시 후 다시 시도해 주세요.',
  },

  index: {
    label1: '라이브 카지노, 스포츠 베팅, 슬롯등 다양한 즐길 거리를 한눈에!',
    label2: '고객센터',
    label3: '더 많은 공지사항',
    label4: '로그인',
    label5: '로그인 후 확인 가능합니다.',
    label6: '입금',
    label7: '계좌이체',
    label8: '출금',
    label9: '라이브 카지노',
    label10: '스포츠 베팅',
    label11: 'e스포츠',
    label12: `테이블
     게임`,
    label13: `슬롯
     게임`,
    label14: '복권',
    label15: '안녕하세요,',
    label16: 'VIP 전용 서비스',
    label17: '회원 전용 혜택을 즉시 누려보세요.',
    label18: '당신만을 위한 특별한 경험을 제공합니다.',
    label19: '회원 센터',
    label20: '메시지',
    label21: '의견 보내기',
    label22: '평생 주소',
    label23: '즉시 로그인',
    label24: '로그아웃',
    label25: '환영합니다!',
    label26: `피싱
    게임`,
    label27: '공지사항',
    label28: '방문을 환영합니다.',
    toast1: '복사가 완료되었습니다!',
  },

  mine: {
    title: '내 정보',
    kefu: '고객센터',
    label1: '중앙 지갑',
    label2: 'VIP 특권',
    label3: '특별 선물 제공',
    label4: '내 지갑',
    label5: '거래 기록',
    label6: '베팅 기록',
    label7: '계정 설정',
    label8: '메시지 센터',
    label9: '환급 센터',
    label10: '이벤트 기록',
    label11: '혜택 센터',
    label12: '제휴 프로그램',
    label13: '함께 성장하며 부를 창출하세요.',
    label14: '파트너 로그인',
    label15: '앱 다운로드',
    label16: '이용안내',
    label17: '로그아웃',
    label18: '가입',
    label19: '제',
    label20: '일째',
    label21: '언어',
    label22: 'USDT 지갑',
    label23: '커미션 지갑',
  },

  transfer: {
    title: '송금',
    label1: '원클릭 회수',
    label2: '로그인',
    label3: '회원가입',
    label4: '게임 플랫폼',
    label5: '자동 전환 모드',
    label6: '금액 변환',
    label7: '입금',
    label8: '출금',
    label9: '제출',
    label10: '금액 변환',
    label11: '플랫폼 계정 잔액',
    label12: '게임 계정 잔액',
    label13: '거래 금액',
    label14: '거래 금액',
    label15: '거래 금액을 입력하세요.',
    label16: '송금 방식',
    label17: '플랫폼 지갑',
    toast1: '거래 금액을 입력해 주세요!',
    toast2: '거래가 성공적으로 완료되었습니다!',
  },

  zanzhu: {
    title: '후원',
    label1: '유벤투스',
    label2: '공식 지역 파트너',
    label3: '아스톤 빌라',
    label4: '공식 글로벌 프리미엄 파트너',
    label5: '더 이상 표시할 내용이 없습니다.',
    label6: '',
    label7: '',
    label8: '',
    label9: '',
    label10: '',
    label11: '',
    label12: '',
    label13: '',

  },
}


export const main = {
  activityInfo: {
    title: '이벤트 상세',
    label1: '이벤트 안내',
    label2: '지금 신청하기',
    label3: '로그인하러 가기',
  },

  activityRecord: {
    title: '이벤트 신청 기록',
    label1: '더 이상 표시할 내용이 없습니다.',
    label2: '이벤트 제목:',
    label3: '신청 시간:',
    label4: '상태:',
    label5: '아직 신청 내역이 없습니다.',
    label6: '미정',
    label7: '심사 대기',
    label8: '승인 완료',
    label9: '거절됨',
    label10: '미정',
  },

  addBankCard: {
    title: '새 은행 계좌번호 추가',
    label1: '계좌 소유자 이름',
    label2: '자금이 신속하게 입금될 수 있도록, 입력한 이름이 은행 계좌의 예금주 이름과 일치하는지 확인해 주세요.',
    label3: '은행 선택',
    label4: '은행 계좌 번호',
    label5: '계좌 개설 지점',
    label6: '결제 비밀번호',
    label7: '추가 확인',
    label8: '은행 선택',
    placeholder1: '계좌 소유자 이름을 입력해 주세요.',
    placeholder2: '은행을 선택해 주세요.',
    placeholder3: '은행 계좌 번호를 입력해 주세요.',
    placeholder4: '계좌 개설 지점을 입력해 주세요.',
    placeholder5: '결제 비밀번호를 입력해 주세요.',
    toast1: '이름을 입력해 주세요.',
    toast2: '은행을 입력해 주세요.',
    toast3: '계좌 개설 지점을 입력해 주세요.',
    toast4: '은행 계좌 번호를 입력해 주세요.',
    toast5: '결제 비밀번호를 입력해 주세요.',
    toast6: '올바른 계좌번호를 입력해 주세요.',
    toast7: '올바른 결제 비밀번호를 입력해 주세요.',
    toast8: '정상적으로 등록되었습니다.',
  },

  addUsdtCard: {
    title: '새로운 USDT 주소 추가',
    label1: 'USDT는 안정적이고 규제 영향을 받지 않습니다.',
    label2: '자세히 알아보기',
    label3: '프로토콜 주소 연결',
    label4: '거래소 이체',
    label5: '출금 완료',
    label6: '지갑 프로토콜 선택 (필수)',
    label7: 'USDT 주소',
    label8: '결제 비밀번호',
    label9: '추가 확인',
    placeholder1: 'USDT 주소를 정확히 입력해 주세요.',
    placeholder2: '결제 비밀번호를 입력해 주세요.',
    toast1: 'USDT 주소를 정확히 입력해 주세요.',
    toast2: '지갑 프로토콜을 선택해 주세요.',
    toast3: '결제 비밀번호를 입력해 주세요.',
  },

  applyagent: {
    title: '제휴 프로그램',
    label1: '제휴 부서',
    label2: '지금 상담하기',
    label3: '상담',
    label4: '함께하세요',
    label5: '사용자명',
    label6: '실명',
    label7: '연락처',
    label8: '신청 사유',
    placeholder1: '연락처를 입력해 주세요.',
    placeholder2: '신청 사유를 입력해 주세요.',
    toast1: '올바른 휴대전화 번호를 입력해 주세요.',
    toast2: '신청 사유를 입력해 주세요.',
  },

  betRecord: {
    title: '베팅 기록',
    label1: '주문번호:',
    label2: '금액',
    label3: '배당금',
    label4: '등록된 내역이 없습니다.',
    label5: '오늘',
    label6: '최근 7일',
    label7: '최근 15일',
    label8: '최근 30일',
    label9: '더 이상 표시할 내용이 없습니다.',
    label10: '무효 베팅',
    label11: '정산 완료',
    label12: '정산 대기',
    label13: '전체 플랫폼',
  },

  boutBallBet: {
    label1: '안녕하세요, 회원님',
    label2: '이용 안내를 잘 살펴보시길 바랍니다.',
    label3: '궁금하신 점이 해결되지 않으셨다면, 온라인 고객센터로 문의해 주세요.',
    label4: '회사 안내',
    label5: '자주 묻는 질문(FAQ)',
    label6: '개인정보 처리방침',
    label7: '면책 안내',
    label8: '1:1 문의하기',
    label9: '파트너 신청',
    label10: '건전한 게임 이용 가이드',
    label11: '해결 방법을 못 찾으셨나요?',
    label12: '고객센터 문의하기',
    label13: '',
  },

  boutBallBetInfo: {
    label4: '자주 묻는 질문(FAQ)',
    label5: '개인정보 처리방침',
    label6: '면책 안내',
    label7: '1:1 문의하기',
    label8: '파트너 신청',
    label9: '회사 안내',
    label10: '건전한 게임 이용 가이드',
    label11: '궁금하신 점이 있으신가요?',
  },

  fanshui: {
    title: '롤링 센터',
    label1: '롤링 기록',
    label2: '클릭하여 수령',
    label3: '누적 수령 금액',
    label4: '수령 대기 중',
    label5: '롤링 금액',
    label6: '롤링 시간',
    label7: '수령 시간:',
    label8: '등록된 내역이 없습니다.',
    label9: '더 이상 표시할 내용이 없습니다.',
    label10: '아직 수령하지 않음',
    toast1: '수령 가능한 금액이 없습니다!',
    label12: '',
  },

  login: {
    title: '로그인',
    label1: '둘러보기',
    label2: '온라인 고객센터',
    label3: '신규 회원 가입',
    label4: '로그인 화면으로 돌아가기',
    label5: '회원가입',
    label6: '계정/비밀번호 저장',
    placeholder1: '휴대폰 번호',
    placeholder2: '비밀번호',
    placeholder3: '보안 코드',
    placeholder4: '휴대폰 번호',
    placeholder5: '로그인 비밀번호',
    placeholder6: '로그인 비밀번호 확인',
    placeholder7: '이름(실명)',
    placeholder8: '결제 비밀번호',
    placeholder9: '보안 코드',
    placeholder10: '초대코드',
    toast1: '사용자명은 6~16자, 영문 또는 숫자 조합으로 입력해 주세요!',
    toast2: '비밀번호는 최소 6자리 이상 입력해 주세요!',
    toast3: '입력한 비밀번호가 일치하지 않습니다!',
    toast4: '실명을 입력해 주세요!',
    toast5: '결제 비밀번호는 최소 6자리 이상 입력해 주세요!',
    toast6: '보안 코드를 입력해 주세요!',
    toast7: '보안 코드가 올바르지 않습니다!',
    toast8: '아이디와 비밀번호를 입력해 주세요!',
    toast9: '초대코드 입력해 주세요!',
  },

  message: {
    title: '메시지 센터',
    label1: '공지사항',
    label2: '쪽지함',
    label3: '더 이상 표시할 내용이 없습니다.',
  },

  money: {
    title: '내 지갑',
    label1: '총 자산 (원)',
    label2: '중앙 지갑 (원)',
    label3: '중앙 지갑 잔액',
    label4: '게임 지갑 잔액',
    label5: '입금',
    label6: '전체 회수',
    label7: '출금',
    label8: '은행 계좌 관리',
    label9: '플랫폼 잔액',
    label10: '이체',
    label11: '새로고침',
  },

  password: {
    title1: '로그인 비밀번호 변경',
    title2: '출금 비밀번호 설정',
    label1: '현재 비밀번호',
    label2: '새 비밀번호',
    label3: '새 비밀번호 확인',
    label4: '변경 확인',
    placeholder1: '현재 비밀번호를 입력해 주세요.',
    placeholder2: '새 비밀번호를 입력해 주세요.',
    placeholder3: '새 비밀번호를 다시 입력해 주세요.',
    toast1: '현재 비밀번호를 입력해 주세요.',
    toast2: '새 비밀번호를 입력해 주세요.',
    toast3: '현재 비밀번호의 길이를 정확히 입력해 주세요.',
    toast4: '새 비밀번호의 길이를 정확히 입력해 주세요.',
    toast5: '비밀번호 확인을 입력해 주세요.',
    toast6: '입력한 비밀번호가 일치하지 않습니다!',
    toast7: '새 비밀번호는 현재 비밀번호와 다르게 설정해 주세요!',
    toast8: '비밀번호가 성공적으로 변경되었습니다!',
  },

  payInfo: {
    label1: '충전 정보',
    label2: '원',
    label3: '다음 시간 내에',
    label4: '결제를 완료해 주세요.',
    label5: '결제가 완료되면 자동으로 충전됩니다!',
    label6: '문의 사항이 있으시면',
    label7: '고객센터로 문의해 주세요.',
    label8: '확인',
    label9: '입금 주소',
    label10: '복사',
    label11: '주문번호',
    label12: '거래 시간',
    label13: '충전 방식',
    label14: '지갑 프로토콜',
    label15: '충전 완료',
    label16: '자금 내역',
    label17: '복사가 완료되었습니다!',
  },

  recharge: {
    title: '입금',
    label1: '입금전용 계좌번호 발급',
    label2: '위챗 페이',
    label3: '알리페이',
    label4: '은행 계좌번호',
    label5: '복사',
    label6: '예금주명',
    label7: '은행명',
    label8: '은행 주소',
    label9: '계좌 개설 은행명',
    label10: '입금자 성함',
    label11: '정확한 입금자명을 입력해 주세요. 잘못 입력시 입금 처리가 지연되거나 실패할 수 있습니다.',
    label12: '은행 계좌번호',
    label13: '계좌 개설 은행 주소',
    label14: '입금 금액',
    label15: '원',
    label16: 'USDT는 가격이 안정적이며 유동성이 높고 규제의 영향을 받지 않습니다.',
    label17: '자세히 알아보기',
    label18: '프로토콜 주소 연결',
    label19: '거래소 이체',
    label20: '출금 완료',
    label21: '지갑 프로토콜',
    label22: '입금 금액',
    label23: '참고 환율',
    label24: '안내 사항',
    label25: '올바른 USDT 프로토콜을 선택하여 결제해 주세요. 잘못된 프로토콜로 결제할 경우, 플랫폼에서 결제를 확인할 수 없으며 이에 대한 책임을 지지 않습니다!',
    label26: '입금 금액',
    label27: '즉시 입금',
    label28: '문제가 있으신가요?',
    label29: '고객센터 문의하기',
    label30: '',
    label31: '안내',
    label32: '아직 지갑을 연결하지 않으셨습니다. 지갑을 먼저 등록해 주세요!',
    label33: '지갑 등록하기',
    label34: '입력 가능한 금액 범위:',
    label35: '사이에서 입력해 주세요!',
    label36: '입금할 은행 계좌번호',
    label37: '아래 계좌로 직접 계좌이체 완료 후 아래 사항을 작성해주시길 바랍니다.',
    placeholder1: '계좌 개설 은행을 선택해 주세요.',
    placeholder2: '입금자 성함을 입력해 주세요.',
    placeholder3: '은행 계좌번호를 입력해 주세요.',
    placeholder4: '계좌 개설 은행 주소를 입력해 주세요.',
    placeholder5: '출금 금액을 입력해 주세요.',
    placeholder6: 'USDT 주소를 입력해 주세요.',
    placeholder7: '출금 금액을 입력해 주세요.',
    placeholder8: '출금 금액을 입력해 주세요.',
    toast1: '입금자 성함을 입력해 주세요.',
    toast2: '은행 유형을 입력해 주세요.',
    toast3: '은행 계좌번호를 입력해 주세요.',
    toast4: '계좌 개설 은행 주소를 입력해 주세요.',
    toast5: '제출이 완료되었습니다. 심사가 진행될 때까지 기다려 주세요.',
    toast6: '복사가 완료되었습니다!',
    other1: '지갑 주소',
    other2: '메모',
    other3: '메모를 입력하세요',
  },

  register: {
    toast1: '관련 약관 및 개인정보 보호 정책을 읽고 동의해 주세요!',
    toast2: '사용자명은 6~16자, 영문 또는 숫자 조합으로 입력해 주세요!',
    toast3: '비밀번호는 최소 6자리 이상 입력해 주세요!',
    toast4: '입력한 비밀번호가 일치하지 않습니다!',
    toast5: '실명을 입력해 주세요!',
    toast6: '결제 비밀번호는 최소 6자리 이상 입력해 주세요!',
  },


  transfer: {
    title: '송금',
    label1: '보유 금액',
    label2: '원클릭 회수',
    label3: '중앙 지갑',
    label4: '게임 지갑',
    label5: '접기',
    label6: '선택',
    label7: '자동전환 설정',
    label8: '기능 활성화 시 잔액이 자동으로 게임머니로 전환됩니다.',
    label9: '게임사별 게임머니 전송은 지원되지 않습니다.',
    label10: '최대 금액',
    label11: '즉시 이체',
    label12: '이체 관련 문제가 발생하셨나요?',
    label13: '고객센터 문의하기',
    label14: '',
    label15: '지갑 선택',
    label16: '플랫폼 지갑',
    toast1: '거래 금액을 입력해 주세요!',
    toast2: '게임사별 게임머니 전송은 지원되지 않습니다.',
    toast3: '거래 금액을 입력해 주세요!',
    toast4: '거래가 성공적으로 완료되었습니다!',
    placeholder1: '이체할 금액을 입력하세요.',
  },

  transRecord: {
    title: '거래 기록',
    label1: '더 이상 표시할 내용이 없습니다.',
    label2: '주문번호:',
    label3: '금액',
    label4: '등록된 내역이 없습니다.',
    label5: '오늘',
    label6: '최근 7일',
    label7: '최근 15일',
    label8: '최근 30일',
    label9: '입금',
    label10: '출금',
    label11: '입금 완료',
    label12: '출금 완료',
    label13: '전체',
    label14: '미정의',
    label15: '심사 대기',
    label16: '심사 승인',
    label17: '심사 거절',
    label18: '실패',
    label19: '성공',
    label20: '정산 대기',
    label21: '미정의',
    label22: '취소',
    label23: '취소',
    label24: '기록',
    label25: '출금 기록'
  },

  usdtmore: {
    title: '프로토콜 차이점',
    label1: '프로토콜 안내',
    label2: 'TRC20: 트론 네트워크 기반 프로토콜',
    label3: 'TRC20은 출금 수수료가 가장 낮아 사용자가 저렴한 비용으로 거래소에서 출금할 수 있습니다. 또한, 트론 네트워크의 TPS(초당 거래 처리량)는 수천 건에 달해 거의 즉시 거래 확인이 가능합니다.',
    label4: 'ERC20: 이더리움 네트워크 기반 프로토콜',
    label5: '이더리움은 자체 토큰과 다양한 토큰을 발행할 수 있습니다. 하지만 수많은 토큰의 규칙이 상이하여 시장 발전에 어려움을 초래할 수 있습니다. 이를 해결하기 위해 토큰 발행자가 만든 스마트 컨트랙트 표준이 바로 ERC20입니다.',
    label6: '프로토콜의 차이점은?',
    label7: '비교 항목',
    label8: 'TRC20 프로토콜',
    label9: 'ERC20 프로토콜',
    label10: '주소 형식',
    label11: 'USDT 주소가 "T"로 시작',
    label12: 'USDT 주소가 "0x"로 시작',
    label13: '사용 네트워크',
    label14: '트론 네트워크',
    label15: '이더리움 네트워크',
    label16: '네트워크 상태',
    label17: '거의 정체 없음',
    label18: '자주 정체 발생',
    label19: '송금 속도',
    label20: '매우 빠름',
    label21: '몇 초에서 몇 분 이내',
    label22: '몇 분에서 수십 분 소요',
    label23: '수수료',
    label24: '낮음',
    label25: '보통',
    label26: '보안 수준',
    label27: '보통',
    label28: '높음',
    label29: '사용 추천',
    label30: '소액·빈번한 거래 추천',
    label31: '거래소 이용 추천',
    label32: '중간 규모 거래 추천',
    label33: '일반적인 거래 추천',
    label34: '어떤 프로토콜을 선택하는 것이 좋을까요?',
    label35: '소액 거래 추천',
    label36: '낮은 수수료, 즉시 입금 가능.',
    label37: '중간 규모 거래 추천',
    label38: '수수료와 속도가 적절한 수준.',
    label39: '각 프로토콜의 USDT 주소는 서로 호환되지 않으므로, 송금 및 충전 시 올바른 주소를 반드시 확인하세요!',
    label40: '보통',
  },

  vip: {
    title: 'VIP 특권',
    label1: '누적 입금액',
    label2: '롤링 요구사항',
    label3: 'VIP 최고 롤링 비율',
    label4: 'VIP 등급',
    label5: '라이브 카지노',
    label6: '스포츠',
    label7: 'e스포츠',
    label8: '보드게임',
    label9: '슬롯',
    label10: '로또',
    label11: '이벤트 규정',
    label12: '승급 기준',
    label13: '회원님의 누적 입금액과 누적 롤링이 해당 등급의 기준을 충족하면, 다음 날 24시 이전에 자동으로 해당 VIP 등급으로 승급됩니다.',
    label14: '승급 순서',
    label15: 'VIP 등급이 해당 기준을 충족하면 하루에 한 단계씩 승급할 수 있으며, 단계를 건너뛰어 승급할 수 없습니다.',
    label16: '등급 유지 조건',
    label17: 'VIP 등급 도달 후, 90일 이내에 정해진 롤링 요구량을 충족해야 합니다. 이 기간 내에 승급하면 등급 유지 조건은 새로운 등급 기준에 맞춰 다시 계산됩니다.',
    label18: '강등 기준',
    label19: '90일(한 분기) 동안 해당 등급의 유지 조건을 충족하지 못하면 시스템이 자동으로 한 단계 하향 조정하며, 이에 따라 롤링 비율 및 기타 혜택도 조정됩니다.',
    label20: 'VIP 롤링 혜택',
    label21: 'VIP 롤링 혜택 금액은 해당 회원이 당일(베이징 시간 00:00~23:59) 동안 발생한 유효 베팅액을 기준으로 계산됩니다. 해당 일의 총 베팅 금액에 대한 롤링 혜택은 베팅 정산이 완료된 후 24시간 이내에 복지 센터에 지급되며, 개인 센터에서 직접 수령할 수 있습니다. (VIP 롤링은 1배 롤링 충족 시 출금 가능)',
    label22: '오보 스포츠는 본 이벤트에 대한 수정, 중단 및 최종 해석권을 보유합니다.',
    label23: '기준 충족',
    label24: '기준 미달',
  },

  userCent: {
    title: '개인 정보',
    label1: '계정 정보를 완성하면 더욱 안전하게 이용할 수 있습니다.',
    label2: '개인 정보 업데이트',
    label3: '정보를 더 정확하게 입력하시면 더욱 편리한 서비스를 제공해 드릴 수 있습니다.',
    label4: '정보 입력하기',
    label5: '은행 계좌번호 관리',
    label6: '출금을 위해 은행 계좌번호 또는 가상화폐 주소를 등록해 주세요.',
    label7: '로그인 비밀번호 관리',
    label8: '로그인 비밀번호를 정기적으로 변경하면 보안이 강화됩니다.',
    label9: '출금 비밀번호 관리',
    label10: '출금 비밀번호를 정기적으로 변경하면 보안이 강화됩니다.',
  },

  userInfo: {
    title: '개인 정보',
    label1: '프로필 사진',
    label2: '사용자명',
    label3: '실명',
    label4: '생년월일',
    label5: '휴대폰 번호',
    label6: '이메일 주소',
    label7: '변경 확인',
    placeholder1: '사용자명을 입력해 주세요.',
    placeholder2: '실명을 입력해 주세요.',
    placeholder3: '생년월일을 선택해 주세요.',
    placeholder4: '휴대폰 번호를 등록하여 계정을 보호하세요.',
    placeholder5: '이메일을 등록하여 계정을 보호하세요.',
    toast1: '올바른 휴대폰 번호를 입력해 주세요.',
    toast2: '올바른 이메일 주소를 입력해 주세요.',
    toast3: '올바른 날짜 형식(YYYY-MM-DD)으로 입력해 주세요.',
    toast4: '정상적으로 처리되었습니다.',
  },

  wallet: {
    title: '은행 계좌 관리',
    label1: '가상화폐',
    label2: '은행 계좌',
    label3: 'USDT 주소 추가',
    label4: '최대 5개의 주소를 등록할 수 있습니다.',
    label5: '은행 계좌 추가',
    label6: '최대 5개의 은행 계좌를 등록할 수 있습니다.',
    toast1: '안내 사항',
    toast2: '해당 계좌의 연결을 해제하시겠습니까?',
    toast3: '연결 해제가 완료되었습니다.',
  },

  welfare: {
    title: '혜택 센터',
    label1: '보너스 기록',
    label2: '수령하러 가기',
    label3: '남은 수령 가능 횟수:',
    label4: '이미 수령한 횟수:',
    label5: '충전 금액',
    label6: '보너스 금액:',
    label7: '충전 시간',
    label8: '수령 시간:',
    label9: '등록된 내역이 없습니다.',
    label10: '더 이상 표시할 내용이 없습니다.',
  },

  withdrawal: {
    title: '출금',
    label1: 'USDT 출금',
    label2: '은행 계좌 출금',
    label3: '지갑 금액',
    label4: '원클릭 회수',
    label5: '중앙 지갑',
    label6: '게임 지갑',
    label7: '접기',
    label8: '펼치기',
    label9: 'USDT 주소 선택',
    label10: 'USDT 주소 추가',
    label11: '롤링 조건',
    label12: '출금 금액',
    label13: '최대 금액',
    label14: '결제 비밀번호',
    label15: '건당 수수료',
    label16: 'USDT 환산 금액',
    label17: '참고 환율:',
    label18: '실시간 변동',
    label19: '실제 입금 금액:',
    label20: '은행 계좌 선택',
    label21: '은행 계좌 추가',
    label22: '은행 계좌를 선택해 주세요.',
    label23: 'USDT 주소를 선택해 주세요.',
    label24: '즉시 출금',
    label25: '출금에 문제가 있으신가요?',
    label26: '고객센터 문의하기',
    label27: '해결하기',
    label28: '커미션 출금',
    placeholder1: '롤링 조건',
    placeholder2: '출금할 금액을 입력하세요.',
    placeholder3: '결제 비밀번호를 입력해 주세요.',
    toast1: '출금할 은행 계좌를 선택하세요',
    toast2: '한 번에 출금할 수 있는 최소 금액은 100원입니다',
    toast3: '결제 비밀번호를 입력하세요',
    toast4: '제출 완료! 관리자 검토를 기다려 주세요',
    toast5: 'USDT 주소를 선택하세요',
    toast6: '한 번에 출금할 수 있는 최소 금액은 10 USDT',
    toast7: '결제 비밀번호를 입력하세요',
    toast8: '제출 완료! 관리자 검토를 기다려 주세요',
  },

  lottery: {
    label1: '잔액',
    label2: '회차',
    label3: '회차 마감',
    label4: '초기화',
    label5: '즉시 베팅',
    label6: '현재 번호',
    label7: '각 베팅 금액',
    label8: '최대 당첨 가능',
    label9: '총',
    label10: '회차 번호',
    label11: '당첨 번호',
    label12: '합계 값',
    label13: '간격',
    label14: '배당률',
    label15: '베팅',
    label16: '더 이상 없습니다',
    toast1: '베팅 점수를 선택하세요',
    toast2: '베팅 금액을 입력하세요',
    toast3: '베팅 기간이 종료되었습니다',
  },
  
  dealRecord: {
    label1: '거래 내역',
    label111: '커미션 기록',
    label2: '내 기록',
    label3: '하위 회원 기록',
    label4: '금액',
    label5: '사용 가능 잔액',
    label6: '확인',
    label7: '취소',
    label8: '시작 시간을 선택하세요',
    label9: '설정하실 초대코드 입력',
    label10: '전체',
    label11: '명',
    toast1: '시작 시간과 종료 시간은 같은 달에 있어야 합니다',
    toast2: '시작 시간은 종료 시간 이전이어야 합니다.',
  },

  coinTrans: {
    label1: '환전',
    label2: 'KRW를 USDT로 전환',
    label3: 'USDT를 KRW로 전환',
    label4: '환전할 금액 입력',
    label5: '출금 비밀번호 입력',
  },

  agent: {
    label1: '에이전트 센터',
    label2: '개요',
    label3: '초대 코드',
    label4: '보고서',
    label5: '팀 인원',
    label6: '에이전트 인원',
    label7: '플레이어 인원',
    label8: '팀 잔액',
    label9: '초대 코드를 입력하세요',
    label10: '충전',
    label11: '출금',
  },

  trans: {
    label1: '이체',
    label2: '정확한 하위 계정 입력',
    label3: '이체하실 금액 입력',
    label4: '출금 비밀번호 입력',
  },

  betList: {
    label1: '베팅 기록',
    label2: '회차 번호',
    label3: '베팅 내용',
    label4: '구매 성공',
    label5: '당첨',
    label6: '미당첨',
    label7: '미개봉',
  },
}


export const other = {
  label1: '알림',
  label2: '로그아웃하시겠습니까?',
  label3: '로그인 해주세요!',
  label4: '복사 완료!',
  label5: '재미있는 콘텐츠가 기다리고 있으니, 서둘러 로그인하세요!',
  label6: '보너스 지급이 종료되었으며',
  label7: '현재 페이지에 머물러 있습니다!',
  label8: '인증 실패',
  label9: '계정 로그인 시간이 만료되었습니다. 다시 로그인해주세요!',
}

export const yingkui = {
  label1: '손익',
  label2: '오늘',
  label3: '어제',
  label4: '그제',
  label5: '최근 7일',
  label6: '최근 15일',
  label7: '수익',
  label8: '배팅 금액',
  label9: '당첨 금액',
  label10: '어제 수익',
}

export const kefu = {
  tip1: '아래에서 원하는 메신저의 고객센터를 클릭하시면 바로 연결됩니다.',
  tip2: '문의하실 내용이 있을 경우, 텔레그램 또는 라인 고객센터를 통해 1:1 상담이 가능하며, 각 메신저 아이디를 클릭하시면 해당 채널로 자동 연결되며, 실시간으로 상담을 도와드립니다.',
  tip3: '※ 운영 시간 외에는 답변이 지연될 수 있는 점 양해 부탁드립니다.',
}