<template>

  <!-- 主体 -->
  <main>
    <van-nav-bar class="pageTop" :title="$t('main.transRecord.label24')" left-arrow @click-left="$router.back()" />
    <div class="container">
      <div class="card">
        <div class="row" @click="checkLanguage('zh_CN')">
          <div class="label">中文（简体）</div>
          <van-icon :name="locale === 'zh_CN' ? 'passed' : 'circle'" size="18" color="# CF876B"></van-icon>
        </div>
        <div class="row" @click="checkLanguage('zh_TW')">
          <div class="label">中文（繁体）</div>
          <van-icon :name="locale === 'zh_TW' ? 'passed' : 'circle'" size="18" color="# CF876B"></van-icon>
        </div>
        <div class="row" @click="checkLanguage('my')">
          <div class="label">Bahasa Melayu</div>
          <van-icon :name="locale === 'my' ? 'passed' : 'circle'" size="18" color="# CF876B"></van-icon>
        </div>
        <div class="row" @click="checkLanguage('en')">
          <div class="label">English</div>
          <van-icon :name="locale === 'en' ? 'passed' : 'circle'" size="18" color="# CF876B"></van-icon>
        </div>
      </div>
    </div>
  </main>
</template>

<script>
// const { t, locale } = useI18n()
export default {
  name: 'setLanguage',
  data() {
    return {
      map: [],
      locale: '',
    };
  },
  created() {
    this.locale = localStorage.getItem('locale')
    this.map = new Map([
      ['en', 'en'],
      ['zh_CN', 'zh_CN'],
      ['zh_TW', 'zh_TW'],
      ['my', 'my'],
    ])
  },
  methods: {
    checkLanguage(val) {
      this.$i18n.locale = val
      localStorage.setItem('locale', val)
      this.locale = val
      window.location.reload()
    }
  },
};


</script>

<style scoped lang="scss">
.pageTop {
  position: relative !important;
  height: 46px;
}
.container {
  margin-top: 10px;
  padding: 10px 15px;
  .card {
    // padding: 10px;
    background: #fff;
  }
  .row {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .label {
      color: #000;
    }
    img {
      width: 1rem;
    }
  }
}
</style>
