<template>
  <div class="treeWrap">
    <div class="treeLable" @click.stop="getChildren(data)">
      <div class="label">
        <van-icon name="arrow" :class="{expand: expand}" v-if="!data.noChild && !loading"/>
        <van-loading type="spinner" size="16" v-if="loading"/>
        <div class="name">{{ data.username }}</div>
      </div>
      <div class="value">{{ data.balance }}</div>
    </div>
    
    <div class="children" v-if="expand">
      <AgentTree :data="data.children" @changeChildren="changeChildren"/>
    </div>
  </div>
</template>
<script>
export default {
  components: { AgentTree: () => import('./agentTree.vue') },
  name: 'agentTreeItem',
  props: {
    data: {
      type: Object,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      expand: false
    }
  },
  methods: {
    async getChildren(item) {
      if(item.children && item.children.length > 0) {
        this.expand = !this.expand
        return
      }
      if(item.noChild) {
        return
      }
      const pid = item.id
      this.loading = true
      const res = await this.$apiFun.agentChildrenApi({pid})
      this.loading = false
      if(res.data.length > 0) {
        res.data = res.data.map(item => {
          item.pid = pid
          return item
        })
      }
      const data = {
        data: res.data,
        pid,
      }
      this.expand = true
      this.$emit('changeChildren', data)
    },
    changeChildren(data){
      this.$emit('changeChildren', data)
    }
  },
};
</script>
<style lang="scss" scoped>
.treeLable {
  display: flex;
  justify-content: space-between;
  border: 1px solid #ccc;
  padding: 10px;
  margin-bottom: 5px;
  border-radius: 5px;
  align-items: center;
  flex-wrap: wrap;
  .label {
    display: flex;
    align-items: center;
    gap: 5px;
  }
}
.children {
  margin-left: 10px;
}
.expand {
  transform: rotate(90deg);
}
</style>
