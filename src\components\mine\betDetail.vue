<template>
    <main>
      <header>
        <van-nav-bar :title="$t('main.betList.label28')" left-arrow @click-left="$router.back()" />
      </header>
      <div class="content">
        <div class="row">
          <div class="label">{{ $t('main.betList.label16') }}：</div>
          <div class="value">{{ detailInfo.trano }}</div>
        </div>
        <div class="row">
          <div class="label">{{ $t('main.betList.label17') }}：</div>
          <div class="value">{{ detailInfo.cp_title }}</div>
        </div>
        <div class="row">
          <div class="label">{{ $t('main.betList.label18') }}：</div>
          <div class="value">{{ detailInfo.playtitle }}</div>
        </div>
        <div class="row">
          <div class="label">{{ $t('main.betList.label19') }}：</div>
          <div class="value">{{ detailInfo.created_at }}</div>
        </div>
        <div class="row">
          <div class="label">{{ $t('main.betList.label20') }}：</div>
          <div class="value">{{ detailInfo.username }}</div>
        </div>
        <div class="row">
          <div class="label">{{ $t('main.betList.label21') }}：</div>
          <div class="value">{{ detailInfo.expect }}{{ $t('main.lottery.label2') }}</div>
        </div>
        <div class="row">
          <div class="label">{{ $t('main.betList.label22') }}：</div>
          <div class="value primary">{{ detailInfo.bet_code }}</div>
        </div>
        <div class="row">
          <div class="label">{{ $t('main.betList.label23') }}：</div>
          <div class="value red">{{ detailInfo.amount }}MYR</div>
        </div>
        <div class="row">
          <div class="label">{{ $t('main.betList.label24') }}：</div>
          <div class="value">{{ detailInfo.opencode }}</div>
        </div>
        <div class="row">
          <div class="label">{{ $t('main.betList.label25') }}：</div>
          <div class="value" :class="`color${detailInfo.openStatus}`">{{ statusInfo[detailInfo.openStatus] }}</div>
        </div>
      </div>
    </main>
  </template>
  <script>
  export default {
    name: 'message',
    data() {
      return {
        detailInfo: {},
        statusInfo: [],
      }
    },
    created() {
      this.statusInfo = [
        this.$t('main.betList.label31'),
        this.$t('main.betList.label29'),
        this.$t('main.betList.label30'),
      ]
      this.detailInfo = JSON.parse(localStorage.getItem('betDetail'))
    },
  };
  </script>
  <style lang="scss" scoped>
    .content {
      padding: 15px 30px;
      .row {
        display: flex;
        height: 30px;
        align-items: center;
        justify-content: space-between;
      }
    }
  </style>
  