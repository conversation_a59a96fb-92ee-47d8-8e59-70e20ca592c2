<template>
  <main>
    <header>
      <van-nav-bar :title="$t('yingkui.label1')" left-arrow @click-left="$router.back()" />
      <div class="right">
        <van-dropdown-menu active-color="#CF866B">
          <van-dropdown-item v-model="params.type" :options="typeList" @change="changeType">
          </van-dropdown-item>
        </van-dropdown-menu>
      </div>
    </header>
    <div class="date">
      <div class="input" :class="{ placeholder: !params2.start_time }" @click="startTimeShow = true">
        <span>{{ params2.start_time || startPlaceholder }}</span>
        <div class="close" @click.stop="params2.start_time = undefined">
          <van-icon name="cross" size="11px"/>
        </div>
      </div>
      <div class="input" :class="{ placeholder: !params2.end_time }" @click="endTimeShow = true">
        <span>{{ params2.end_time || endTimePlaceholder }}</span>
        <div class="close" @click.stop="params2.end_time = undefined">
          <van-icon name="cross" size="11px"/>
        </div>
      </div>
    </div>

    <div class="wrap">
      <div class="line">
        <div class="card">
          <div class="title">{{ title }} {{ $t('yingkui.label7') }}</div>
          <div class="price" :class="{ red: value < 0, green: value > 0 }">
            <span>RM</span>
            <countTo :startVal='startValue' :endVal='value' :duration='2000'></countTo>
          </div>
          <div class="row">
            <div class="label">{{ $t('yingkui.label8') }}:</div>
            <div class="val">RM {{ bet_money }}</div>
          </div>
          <div class="row">
            <div class="label">{{ $t('yingkui.label9') }}:</div>
            <div class="val">RM {{ win_money }}</div>
          </div>
        </div>
      </div>
      <div class="yes">
        <div class="title">{{ $t('yingkui.label10') }}</div>
        <div class="price" :class="{ red: yesValue < 0, green: yesValue > 0 }">RM {{ yesValue }}</div>
      </div>
    </div>



    <van-popup v-model="startTimeShow" position="bottom">
      <van-datetime-picker
        v-model="currentDate"
        type="date"
        :title="$t('yingkui.label11')"
        :min-date="minDate"
        :max-date="maxDate"
        :confirm-button-text="$t('main.dealRecord.label6')"
        :cancel-button-text="$t('main.dealRecord.label7')"
        @confirm="selectStartDate"
      />
    </van-popup>

    <van-popup v-model="endTimeShow" position="bottom">
      <van-datetime-picker
        v-model="currentDate"
        type="date"
        :title="$t('yingkui.label12')"
        :min-date="minDate"
        :max-date="maxDate"
        :confirm-button-text="$t('main.dealRecord.label6')"
        :cancel-button-text="$t('main.dealRecord.label7')"
        @confirm="selectEndDate"
      />
    </van-popup>
  </main>
</template>
<script>
import countTo from 'vue-count-to';
export default {
  components: { countTo },
  name: 'message',
  data() {
    return {
      params: {
        type: 0,
      },
      startValue: 0,
      value: 0,
      yesValue: 0,
      bet_money: 0,
      win_money: 0,
      currentDate: new Date(),
      minDate: new Date(2020,0,1),
      maxDate: new Date(2030,0,1),
      startTimeShow: false,
      endTimeShow: false,
      startPlaceholder: '',
      type: 1,
      params2: {
        start_time: '',
        end_time: '',
      },
    }
  },
  created() {
    this.startPlaceholder = this.$t('yingkui.label11')
    this.endTimePlaceholder =  this.$t('yingkui.label11')
    this.typeList = [
      { text: this.$t('yingkui.label2'), value: 0 },
      { text: this.$t('yingkui.label4'), value: 2 },
      { text: this.$t('yingkui.label5'), value: 6 },
      { text: this.$t('yingkui.label6'), value: 14 },
    ]
    this.$parent.loading = true
    this.getData()
    this.getYesterdayData()
  },
  computed: {
    title (){
      return this.type === 1 ? this.typeList.filter(item => item.value === this.params.type)[0].text : this.params2.search_time
    }
  },
  methods: {
    async getData() {
      const res = await this.$apiFun.getYingkuiApi(this.params)
      this.$parent.loading = false
      this.value = res.data.win_loss
      this.bet_money = res.data.bet_money
      this.win_money = res.data.win_money
      this.type = 1
    },

    changeType(e) {
      this.$parent.loading = true
      this.params.type = e
      this.getData()
    },
    async getYesterdayData() {
      const res = await this.$apiFun.getYingkuiApi({type: 1})
      this.yesValue = res.data.win_loss
    },
    async selectStartDate(e) {
      this.startTimeShow = false
      const date = this.formatDate(e)
      this.params2.start_time = date
      this.resetDate()
    },
    async selectEndDate(e) {
      this.endTimeShow = false
      const date = this.formatDate(e)
      this.params2.end_time = date
      this.resetDate()
    },

    async resetDate()  {
      if(this.params2.start_time === '' || this.params2.end_time === '') {
        return
      }
      this.$parent.loading = true
      const res = await this.$apiFun.getYingkui2Api(this.params2)
      this.value = res.data.win_loss
      this.bet_money = res.data.bet_money
      this.win_money = res.data.win_money
      this.$parent.loading = false
    },

    
    formatDate(e, type) {
      const n = '-'
      const date = new Date(e)
      const year = date.getFullYear()
      const month = date.getMonth() + 1 < 10 ? `0${Number(date.getMonth()) + 1}` : Number(date.getMonth()) + 1
      const r = `${year}${n}${month}`
      return r
    },
  },
  beforeDestroy() {},
};
</script>
<style lang="scss" scoped>
main {
  min-height: calc(var(--vh) * 100);
  background: linear-gradient(180deg, #EFF2FE 5.46%, #E8EDFF 30.35%, #F6F8FF 97.83%);;
}
.van-nav-bar {
  background: transparent !important;
}
::v-deep .van-nav-bar__title {
  color: #5D83B2 !important;
}
.date {
  position: relative;
  z-index: 10;
  margin: 0 auto;
}
header {
  height: 60px;
  .van-nav-bar {
    position: fixed;
    width: 100%;
    top: 0;
  }
  .right {
    position: fixed;
    display: flex;
    align-items: center;
    z-index: 1000;
    height: 46px;
    line-height: 46px;
    right: 20px;
    font-weight: 500;
    font-size: .35rem;
  }
}

.wrap {
  margin-top: 10vw;
  .line{
    position: fixed;
    width: 232vw;
    height: 232vw;
    border: 2px solid #fff;
    border-radius: 50%;
    top: -150vw;
    left: calc(100vw - 166.7vw);
    background: #F4F6FC;
    overflow: hidden;
  }

  .card {
    background: linear-gradient(180deg, #EFF2FE 0%, #E8EDFF 100%);
    border: 2px solid #fff;
    border-bottom: 0;
    padding: 5.3vw 6.9vw;
    border-radius: 20px 20px 0 0;
    margin: 6.9vw;
    height: 68.83vw;
    box-sizing: border-box;
    
    
    position: relative;
    top: 169.7vw;
    width: calc(100vw - 13.95vw);
    box-sizing: border-box;
    left: 66.2vw;
    .row {
      display: flex;
      padding: 10px 0;
      align-items: center;
      .label {
        color: #5D75AB;
        margin-right: 10px;
        font-size: 14px;
        line-height: 14px;
      }
      .val {
        color: #000;
        font-size: 14px;
        line-height: 14px;
      }
    }
  }

  .yes {
    position: relative;
    top: 65vw;
  }

  .title {
    text-align: center;
    color: #5D75AB;
    font-size: 16px;
  }
  .price {
    font-size: 26px;
    text-align: center;
    color: #888;
    padding: 10px 0;
    span {
      font-size: 26px;
      text-align: center;
      color: #888;
      padding: 10px 0;
    }
  }
    .red {
      color: #C41717;
      span {
        color: #C41717; 
      }
    }
    .green {
      color: #00AC2E;
      span {
        color: #00AC2E; 
      }
    }
}

.date {
  display: flex;
  margin: 0 15px;
  margin-top: 10px;
  gap: 15px;
  .input {
    border: 1px solid #ccc;
    flex: 1;
    padding: 10px;
    border-radius: 5px;
    text-align: center;
    position: relative;
  }
  .placeholder {
    color: #bbb;
  }
  .close {
    position: absolute;
    top: 10px;
    right: 10px;
    height: 16px;
    width: 16px;
    background: #ccc;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: .35rem;
  }
}

</style>


<style lang="scss">

.van-dropdown-menu__bar {
  height: 17px !important;
  box-shadow: none !important;
  background: none !important;
}

.van-dropdown-item {
  top: 46px !important;
  .van-cell__title span {
    font-size: .34rem !important;
  }
}

.van-dropdown-menu {
  height: 17px !important;

  .van-ellipsis {
    font-size: .4rem;
    font-weight: 500;
  }
}

.van-dropdown-menu__title:after {
  border-color: transparent transparent #333 #333;
}

.van-dropdown-menu__title--active:after {
  border-color: transparent transparent currentColor currentColor;
}
</style>
